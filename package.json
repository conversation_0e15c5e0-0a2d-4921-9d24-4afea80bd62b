{"name": "pdfily", "version": "0.1.0", "private": true, "scripts": {"clean:workspaces": "git clean -xdf .turbo node_modules", "format": "prettier --write \"**/*.{ts,tsx,md,mdx}\"", "format:write": "prettier --write \"**/*.{ts,tsx,md,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,md,mdx}\" --cache", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "check-types": "turbo run check-types", "prebuild": "pnpm run prepare:web-assets", "predev": "pnpm run prepare:web-assets", "prepare:web-assets": "pnpm run update:web-assets:nutrient && pnpm run update:web-assets:pdftron", "update:web-assets:nutrient": "sh ./scripts/update-nutrient-webviewer-assets.sh", "update:web-assets:pdftron": "sh ./scripts/update-pdftron-webviewer-assets.sh", "set-supabase-secrets": "sh ./scripts/set-supabase-secrets.sh", "create:s3-bucket": "sh ./scripts/create-storage-bucket.sh", "dev": "pnpm run --filter=@pdfily/web dev", "build": "pnpm run --filter=@pdfily/web build", "start": "pnpm run --filter=@pdfily/web start", "preview": "pnpm build && pnpm start", "supabase:secrets": "pnpm run set-supabase-secrets", "supabase:secrets:dev": "pnpm run --filter=@pdfily/web supabase:secrets:dev", "supabase:secrets:stage": "pnpm run --filter=@pdfily/web supabase:secrets:stage", "supabase:secrets:prod": "pnpm run --filter=@pdfily/web supabase:secrets:prod", "supabase:start": "pnpm run --filter=@pdfily/web supabase:start", "supabase:stop": "pnpm run --filter=@pdfily/web supabase:stop", "supabase:typegen": "pnpm run --filter=@pdfily/web supabase:typegen", "supabase:db:reset": "pnpm run --filter=@pdfily/web supabase:db:reset", "supabase:deploy": "pnpm run --filter=@pdfily/web supabase:deploy"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.31.0", "eslint-config-next": "^15.4.4", "eslint-config-prettier": "^10.1.8", "prettier": "^3.6.2", "turbo": "^2.5.5"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=20"}, "sideEffects": false}