'use client';

import { getSupabaseBrowserClient } from '@pdfily/supabase/browser-client';
import { Button } from '@pdfily/ui/button';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Swal from 'sweetalert2';

const GoogleSignIn = () => {
  const [popup, setPopup] = useState<Window | null>(null);
  const router = useRouter();

  useEffect(() => {
    // If there is no popup, nothing to do
    if (!popup) return;

    // Listen for messages from the popup using postMessage
    const handleMessage = (event: MessageEvent) => {
      // Check origin for security
      if (event.origin !== window.location.origin) return;

      const { type, redirectTo, error } = event.data;

      if (type === 'GOOGLE_AUTH_SUCCESS') {
        console.log('Authentication successful, redirecting to:', redirectTo);
        setPopup(null);
        // Redirect to the specified path or home
        router.replace(redirectTo || '/');
      } else if (type === 'GOOGLE_AUTH_ERROR') {
        console.error('Authentication error:', error);
        setPopup(null);
        Swal.fire({
          icon: 'error',
          title: 'Authentication Error',
          text: error || 'Failed to authenticate with Google. Please try again.',
        });
      }
    };

    window.addEventListener('message', handleMessage);

    // effect cleaner (when component unmount)
    return () => {
      window.removeEventListener('message', handleMessage);
      setPopup(null);
    };
  }, [popup, router]);

  const login = async () => {
    const supabase = getSupabaseBrowserClient();
    const origin = location.origin;
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${origin}/auth/popup-callbacks`,
        queryParams: { prompt: 'select_account' },
        skipBrowserRedirect: true,
      },
    });
    if (error || !data) {
      return Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to sign in with Google. Please try again.',
      });
    }

    const popup = openPopup(data.url);
    setPopup(popup);
  };

  const openPopup = (url: string) => {
    const width = 500;
    const height = 600;
    const left = window.screen.width / 2 - width / 2;
    const top = window.screen.height / 2 - height / 2;

    // window features for popup
    const windowFeatures = `scrollbars=no, resizable=no, copyhistory=no, width=${width}, height=${height}, top=${top}, left=${left}`;
    const popup = window.open(url, 'popup', windowFeatures);
    return popup;
  };

  return (
    <Button onClick={login} variant="outline">
      Google Login {popup ? 'processing...' : ''}
    </Button>
  );
};

export default GoogleSignIn;
