'use client';

import { getSupabaseBrowserClient } from '@pdfily/supabase/browser-client';
import { Button } from '@pdfily/ui/button';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Swal from 'sweetalert2';

const GoogleSignIn = () => {
  const [popup, setPopup] = useState<Window | null>(null);
  const router = useRouter();

  useEffect(() => {
    // If there is no popup, nothing to do
    if (!popup) return;

    const getDataFromPopup = (e: MessageEvent) => {
      // check origin
      if (e.origin !== window.location.origin) return;

      // get authResultCode from popup
      const code = e.data?.authResultCode;
      if (!code) return;

      // clear popup and replace the route
      setPopup(null);
      router.replace(`/auth/callback?code=${code}`);
    };

    // Listen for messages from the popup by creating a BroadcastChannel
    const channel = new BroadcastChannel('popup-channel');
    channel.addEventListener('message', getDataFromPopup);

    // effect cleaner (when component unmount)
    return () => {
      channel.removeEventListener('message', getDataFromPopup);
      setPopup(null);
    };
  }, [popup, router]);

  const login = async () => {
    const supabase = getSupabaseBrowserClient();
    const origin = location.origin;
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${origin}/auth/popup-callbacks`,
        queryParams: { prompt: 'select_account' },
        skipBrowserRedirect: true,
      },
    });
    if (error || !data) {
      return Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to sign in with Google. Please try again.',
      });
    }

    const popup = openPopup(data.url);
    setPopup(popup);
  };

  const openPopup = (url: string) => {
    const width = 500;
    const height = 600;
    const left = window.screen.width / 2 - width / 2;
    const top = window.screen.height / 2 - height / 2;

    // window features for popup
    const windowFeatures = `scrollbars=no, resizable=no, copyhistory=no, width=${width}, height=${height}, top=${top}, left=${left}`;
    const popup = window.open(url, 'popup', windowFeatures);
    return popup;
  };

  return (
    <Button onClick={login} variant="outline">
      Google Login {popup ? 'processing...' : ''}
    </Button>
  );
};

export default GoogleSignIn;
