import { NextResponse } from 'next/server';
import { getLocale } from 'next-intl/server';

import pathsConfig from '@pdfily/config/paths.config';
import { createAuthCallbackService } from '@pdfily/supabase/auth';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';

import { redirect } from '@/lib/i18n/navigation';

/**
 * Handles GET requests for the auth callback.
 *
 * This route is required for the server-side authentication flow implemented by the SSR package.
 * It exchanges an authorization code for the user's session.
 * For more details, see: https://supabase.com/docs/guides/auth/server-side/nextjs
 *
 * @param {Request} request - The incoming HTTP request object.
 * @returns {Promise<NextResponse>} A promise that resolves to a NextResponse, redirecting the user appropriately.
 */
export async function GET(request: Request): Promise<NextResponse> {
  const client = getSupabaseServerClient();
  const service = createAuthCallbackService(client);

  const { nextPath } = await service.exchangeCodeForSession(request, {
    redirectPath: pathsConfig.app.home,
  });

  const locale = await getLocale();
  return redirect({ href: nextPath, locale });
}
