import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@pdfily/supabase/server-admin-client';

/**
 * @name POST
 * @description Handles document ownership transfer from anonymous user to authenticated user.
 * This endpoint is called from the client-side popup auth flow.
 * 
 * @param {NextRequest} request - The incoming HTTP request object.
 * @returns {Promise<NextResponse>} A promise that resolves to a NextResponse.
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { fromUserId, toUserId } = await request.json();

    if (!fromUserId || !toUserId) {
      return NextResponse.json(
        { error: 'Both fromUserId and toUserId are required' },
        { status: 400 }
      );
    }

    if (fromUserId === toUserId) {
      return NextResponse.json(
        { error: 'fromUserId and toUserId cannot be the same' },
        { status: 400 }
      );
    }

    const adminSupabase = getSupabaseServerAdminClient();

    // Check if there are any documents to transfer
    const { data: documentsToTransfer, error: checkError } = await adminSupabase
      .from('documents')
      .select('id')
      .eq('user_id', fromUserId);

    if (checkError) {
      console.error('Error checking documents for transfer:', checkError);
      return NextResponse.json(
        { error: 'Failed to check documents for transfer' },
        { status: 500 }
      );
    }

    if (documentsToTransfer && documentsToTransfer.length > 0) {
      // Transfer document ownership
      const { error: updateError } = await adminSupabase
        .from('documents')
        .update({ user_id: toUserId })
        .eq('user_id', fromUserId);

      if (updateError) {
        console.error('Error updating document ownership:', updateError);
        return NextResponse.json(
          { error: 'Failed to transfer document ownership' },
          { status: 500 }
        );
      }

      console.info(`Successfully transferred ${documentsToTransfer.length} documents`, {
        fromUserId,
        toUserId,
        documentCount: documentsToTransfer.length,
        context: 'api.document.transfer',
      });

      return NextResponse.json({
        success: true,
        transferredCount: documentsToTransfer.length,
        message: `Successfully transferred ${documentsToTransfer.length} documents`,
      });
    } else {
      console.info('No documents found for transfer', {
        fromUserId,
        toUserId,
        context: 'api.document.transfer',
      });

      return NextResponse.json({
        success: true,
        transferredCount: 0,
        message: 'No documents found for transfer',
      });
    }
  } catch (error) {
    console.error('Unexpected error during document transfer:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
