import Image from 'next/image';

import { cn } from '@pdfily/ui/utils';
import { Link } from '@/lib/i18n/navigation';

function LogoImage({ className, width = 121, height = 36 }: { className?: string; width?: number; height?: number }) {
  return (
    <Image
      src="/images/header/Logo.svg"
      alt="PDFily Logo"
      width={width}
      height={height}
      priority
      className={cn('object-contain', 'tablet:h-[26px] tablet:w-[87px]', 'final:h-[26px] final:w-[87px]', className)}
    />
  );
}

export function AppLogo({ href, label, className }: { href?: string | null; className?: string; label?: string }) {
  if (href === null) {
    return <LogoImage className={className} />;
  }

  return (
    <Link
      aria-label={label ?? 'Home'}
      href={href ?? '/'}
      className={cn('h-9 w-[121px]', 'tablet:h-[26px] tablet:w-[87px]', 'final:h-[26px] final:w-[87px]')}
    >
      <LogoImage className={className} />
    </Link>
  );
}
