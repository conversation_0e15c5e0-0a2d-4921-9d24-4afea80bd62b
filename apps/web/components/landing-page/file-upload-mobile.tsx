'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useRef, type ChangeEvent } from 'react';
import { Button } from '@pdfily/ui/button';
import { cn } from '@pdfily/ui/utils';
import { usePostHogEvents } from '@/lib/tracking/posthog/usePostHogEvents';

interface FileUploadBoxProps {
  onFilesSelected: (files: File[]) => void;
  maxFileSize?: number; // in MB
  onError?: (error: string) => void;
  accept?: string; // File types to accept (e.g., '.pdf', '.jpg', 'image/*')
}

export default function FileUploadMobile({
  onFilesSelected,
  maxFileSize = 50,
  onError,
  accept: accept = '.pdf,application/pdf',
}: FileUploadBoxProps) {
  const { trackPostHogUploadFileEvent } = usePostHogEvents();
  const t = useTranslations('HomePage.bannerSection');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const acceptedTypes = accept.split(',').map((type) => type.trim().toLowerCase());
  const maxSizeBytes = maxFileSize * 1024 * 1024;

  /**
   * Validate and filter accepted files by type and size
   */
  const validateFiles = (files: File[]): File[] => {
    const validFiles: File[] = [];
    const errors: string[] = [];

    files.forEach((file) => {
      const fileType = file.type.toLowerCase();
      const fileExtension = file.name.split('.').pop()?.toLowerCase();

      const isTypeAccepted = acceptedTypes.some(
        (type) =>
          type === fileType ||
          (type.startsWith('.') && fileExtension === type.slice(1)) ||
          (type.endsWith('/*') && fileType.startsWith(type.replace('/*', ''))),
      );

      if (!isTypeAccepted) {
        errors.push(`File "${file.name}" is not an accepted type.`);
        return;
      }

      if (file.size > maxSizeBytes) {
        errors.push(`File "${file.name}" exceeds the ${maxFileSize} MB limit.`);
        return;
      }

      validFiles.push(file);
    });

    if (errors.length) {
      const message = errors.join(' ');
      onError?.(message);
    }

    return validFiles;
  };

  /**
   * Handle file input change event for file selection
   *
   * @param event - ChangeEvent from file input
   */
  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (!files.length) return;

    // Validate file sizes
    const validFiles = validateFiles(files);

    if (validFiles.length > 0 && onFilesSelected) {
      onFilesSelected(validFiles);
    }

    // Track upload file event
    trackPostHogUploadFileEvent({});

    // Reset the input so the same file can be selected again
    e.target.value = '';
  };

  /**
   * Handle button click to open file dialog
   */
  const handleButtonClick = () => fileInputRef.current?.click();

  return (
    <>
      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileInputChange}
        accept={accept}
        multiple
        className="hidden"
      />

      <Button
        onClick={handleButtonClick}
        className={cn(
          'mt-8 flex h-[50px] w-full items-center justify-center gap-2 rounded-[10px] bg-[#F0401D] hover:bg-red-600',
          'hidden tablet:flex final:flex',
        )}
      >
        <Image
          src="/images/landing-page/banner-section/UploadIcon.svg"
          alt="Upload Icon"
          width={20}
          height={20}
          className="object-contain"
        />
        <span className="font-onest text-[16px] font-medium leading-[20px] tracking-normal text-white">
          {t('mobileUploadButton')}
        </span>
      </Button>
    </>
  );
}
