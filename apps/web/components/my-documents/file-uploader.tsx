'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { ChangeEvent, useRef, useState } from 'react';
import { cn } from '@pdfily/ui/utils';
import { Button } from '@pdfily/ui/button';
import { usePostHogEvents } from '@/lib/tracking/posthog/usePostHogEvents';

interface FileUploaderProps {
  onFilesSelected: (files: File[]) => void;
  maxFileSize?: number; // in MB
  onError?: (error: string) => void;
  accept?: string; // File types to accept (e.g., '.pdf', '.jpg', 'image/*')
}

export default function FileUploader({
  onFilesSelected,
  maxFileSize = 50,
  onError,
  accept: accept = '.pdf,application/pdf',
}: FileUploaderProps) {
  const { trackPostHogUploadFileEvent } = usePostHogEvents();
  const t = useTranslations('MyDocuments');
  const [isDragging, setIsDragging] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const acceptedTypes = accept.split(',').map((type) => type.trim().toLowerCase());
  const maxSizeBytes = maxFileSize * 1024 * 1024;

  /**
   * Validate and filter accepted files by type and size
   */
  const validateFiles = (files: File[]): File[] => {
    const validFiles: File[] = [];
    const errors: string[] = [];

    files.forEach((file) => {
      const fileType = file.type.toLowerCase();
      const fileExtension = file.name.split('.').pop()?.toLowerCase();

      const isTypeAccepted = acceptedTypes.some(
        (type) =>
          type === fileType ||
          (type.startsWith('.') && fileExtension === type.slice(1)) ||
          (type.endsWith('/*') && fileType.startsWith(type.replace('/*', ''))),
      );

      if (!isTypeAccepted) {
        errors.push(`File "${file.name}" is not an accepted type.`);
        return;
      }

      if (file.size > maxSizeBytes) {
        errors.push(`File "${file.name}" exceeds the ${maxFileSize} MB limit.`);
        return;
      }

      validFiles.push(file);
    });

    if (errors.length) {
      const message = errors.join(' ');
      setErrorMessage(message);
      onError?.(message);

      // Clear error message after 5 seconds
      setTimeout(() => setErrorMessage(null), 5000);
    }

    return validFiles;
  };

  /**
   * Handle drag enter event to activate dragging state
   *
   * @param event - DragEvent from drag enter
   */
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  /**
   * Handle drag leave event to deactivate dragging state
   *
   * @param event - DragEvent from drag leave
   */
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  /**
   * Handle drag over event to allow dropping and activate dragging state
   *
   * @param event - DragEvent from drag over
   */
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) setIsDragging(true);
  };

  /**
   * Handle drop event for drag-and-drop file upload
   *
   * @param event - DragEvent from dropping files
   */
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files || []);
    if (!files.length) return;

    // Directly validate files (type + size)
    const validFiles = validateFiles(files);

    if (validFiles.length > 0 && onFilesSelected) {
      onFilesSelected(validFiles);
    }
  };

  /**
   * Handle file input change event for file selection
   *
   * @param event - ChangeEvent from file input
   */
  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (!files.length) return;

    // Validate file sizes
    const validFiles = validateFiles(files);

    if (validFiles.length > 0 && onFilesSelected) {
      onFilesSelected(validFiles);
    }

    // Track upload file event
    trackPostHogUploadFileEvent({});

    // Reset the input so the same file can be selected again
    e.target.value = '';
  };

  /**
   * Handle button click to open file dialog
   */
  const handleButtonClick = () => fileInputRef.current?.click();

  return (
    <div
      className={cn(
        'my-8 h-[464px] w-screen max-w-[1060px] rounded-3xl p-4',
        'bg-white shadow-[2px_8px_20px_0px_#84899226]',
        'desktop:mx-4 desktop:my-6 desktop:h-auto desktop:w-[calc(100vw_-_2rem)] desktop:p-3',
        'final:mx-4 final:my-6 final:h-auto final:w-[calc(100vw_-_2rem)] final:p-3',
      )}
    >
      <div
        className={cn(
          'h-full w-full rounded-3xl border-2 border-dashed border-[#A6A9AB] p-6',
          'flex flex-col items-center justify-center',
          isDragging && 'border-[#F0401D]',
          errorMessage && 'border-red-500',
        )}
        onDragEnter={handleDragEnter}
      >
        {/* Drag overlay */}
        {isDragging && (
          <div
            className="fixed inset-0 z-10 flex flex-col items-center justify-center gap-4 bg-black/80"
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Image
              src="/images/landing-page/banner-section/Upload.svg"
              alt="Upload Icon"
              width={52}
              height={52}
              className="pointer-events-none object-contain"
            />
            <span className="pointer-events-none font-onest text-[40px] font-medium leading-[44px] tracking-[-0.05px] text-white">
              {t('fileUploader.dropFiles')}
            </span>
          </div>
        )}

        {/* Error message overlay */}
        {errorMessage && (
          <div className="absolute left-0 right-0 top-4 flex justify-center">
            <div className="flex items-center rounded-md border border-red-400 bg-red-100 px-4 py-2 text-red-700">
              <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
              {errorMessage}
            </div>
          </div>
        )}
        <div
          className={cn(
            'flex h-[232px] w-[345px] flex-col items-center gap-[28px]',
            'desktop:h-full desktop:w-full desktop:flex-col-reverse desktop:gap-0',
            'final:h-full final:w-full final:flex-col-reverse final:gap-0',
            'cursor-pointer',
          )}
          onClick={handleButtonClick}
        >
          <Button
            className={cn(
              'flex h-12 w-full items-center justify-center rounded-[10px] bg-[#F0401D] hover:bg-red-600',
              'hidden',
              'desktop:flex',
              'final:flex',
            )}
          >
            <span className="font-onest text-[16px] font-medium leading-[20px] tracking-normal text-white">
              {t('fileUploader.uploadDocument')}
            </span>
          </Button>

          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileInputChange}
            accept=".pdf,application/pdf"
            multiple
            className="hidden"
          />

          <Image
            src="/images/landing-page/banner-section/UploadIllustration.svg"
            alt="Upload illustration"
            width={218}
            height={156}
            className={cn(
              'object-contain',
              'desktop:mb-4 desktop:h-[110px] desktop:w-[154px]',
              'final:mb-4 final:h-[110px] final:w-[154px]',
            )}
          />

          <p className={cn('font-onest text-lg font-normal leading-6 text-black', 'desktop:hidden', 'final:hidden')}>
            {t('fileUploader.workspaceReady')}{' '}
            <span className={cn('text-[#F0401D]', 'desktop:text-black', 'final:text-black')}>
              {t('fileUploader.uploadOrDrop')}
            </span>{' '}
            {t('fileUploader.toGetStarted')}
          </p>

          <p
            className={cn(
              'mb-[21px] hidden text-center font-onest text-sm font-normal leading-5 text-[#1C1C1C]',
              'desktop:block',
              'final:block',
            )}
          >
            {t('fileUploader.workspaceReady')}
            <br />
            {t('fileUploader.uploadToGetStarted')}
          </p>
        </div>
      </div>
    </div>
  );
}
