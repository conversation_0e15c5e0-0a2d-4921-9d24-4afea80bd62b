'use client';

import dynamic from 'next/dynamic';
import { ThemeProvider } from 'next-themes';

import { CaptchaWrapperProvider } from '@pdfily/auth/captcha/client';
import appConfig from '@pdfily/config/app.config';
import authConfig from '@pdfily/config/auth.config';
import { If } from '@pdfily/ui/if';

import { AuthProvider } from './auth-provider';
import { ReactQueryProvider } from './react-query-provider';
import { SessionProvider } from './session-provider';
import { AnonymousProvider } from './anonymous-provider';
import SearchParamsProvider from './search-params-provider';

// Retrieve the captcha site key from the app config
const captchaSiteKey = authConfig.captchaTokenSiteKey;

/**
 * Dynamically imports the CaptchaTokenSetter component.
 */
const CaptchaTokenSetter = dynamic(async () => {
  if (!captchaSiteKey) {
    return Promise.resolve(() => null);
  }
  const { CaptchaTokenSetter } = await import('@pdfily/auth/captcha/client');

  return {
    default: CaptchaTokenSetter,
  };
});

export function RootProviders({
  theme = appConfig.theme,
  lang = appConfig.lang,
  children,
}: React.PropsWithChildren<{
  theme?: string;
  lang?: string;
  children: React.ReactNode;
}>) {
  return (
    <ReactQueryProvider>
      <CaptchaWrapperProvider>
        <If condition={authConfig.captchaEnabled}>
          <CaptchaTokenSetter siteKey={captchaSiteKey} lang={lang} />
        </If>

        <AuthProvider>
          <AnonymousProvider>
            <SessionProvider>
              <ThemeProvider attribute="class" defaultTheme={theme} enableSystem disableTransitionOnChange>
                <SearchParamsProvider />
                {children}
              </ThemeProvider>
            </SessionProvider>
          </AnonymousProvider>
        </AuthProvider>
      </CaptchaWrapperProvider>
    </ReactQueryProvider>
  );
}
