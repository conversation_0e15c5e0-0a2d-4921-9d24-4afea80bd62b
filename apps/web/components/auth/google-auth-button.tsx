'use client';

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { ComponentProps, useState, useEffect, useCallback } from 'react';
import { cn } from '@pdfily/ui/utils';
import authConfig from '@pdfily/config/auth.config';

import Swal from 'sweetalert2';

import { Button } from '../ui/button';

/**
 * Props interface for the GoogleAuthButton component.
 *
 * @interface GoogleAuthButtonProps
 * @extends {React.ButtonHTMLAttributes<HTMLButtonElement>}
 */
type GoogleAuthButtonProps = ComponentProps<typeof Button> & {
  /**
   * Additional CSS classes to apply to the button.
   * @type {string}
   * @default ''
   */
  className?: string;

  path: string;
};

/**
 * GoogleAuthButton component that renders a Google authentication button with visual separator.
 *
 * The component only renders when Google authentication is enabled in the auth configuration.
 * When disabled, it returns an empty fragment to maintain layout consistency.
 * Uses popup-based authentication to avoid page redirects.
 */
export default function GoogleAuthButton({ path, className = '', ...rest }: GoogleAuthButtonProps) {
  const t = useTranslations('Auth');
  const { googleAuthEnabled } = authConfig;
  const [isLoading, setIsLoading] = useState(false);
  const [popup, setPopup] = useState<Window | null>(null);

  const handleGoogleSignIn = async () => {
    if (isLoading) return;

    setIsLoading(true);

    try {
      // Ouvrir la popup immédiatement pour éviter le blocage Safari
      const newPopup = openPopup('about:blank');
      setPopup(newPopup);

      // Utiliser l'API server-side pour obtenir l'URL OAuth (comme avant)
      const response = await fetch('/api/auth/sign-in-with-provider', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          provider: 'google',
          path,
          usePopup: true, // Important : indique que c'est pour une popup
        }),
      });

      const data = await response.json();

      if (data.error) {
        newPopup?.close();
        setPopup(null);
        throw new Error(data.error);
      }

      // Naviguer SEULEMENT la popup vers l'URL OAuth
      if (data.url) {
        if (newPopup && !newPopup.closed) {
          newPopup.location.href = data.url;
        } else {
          throw new Error('Popup was blocked or closed');
        }
      } else {
        newPopup?.close();
        setPopup(null);
        throw new Error('No authentication URL received');
      }
    } catch (error) {
      console.error('Google sign-in error:', error);
      setIsLoading(false);
      Swal.fire({
        icon: 'error',
        title: 'Erreur',
        text: 'Échec de la connexion avec Google. Veuillez réessayer.',
      });
    }
  };

  const openPopup = (url: string) => {
    const width = 500;
    const height = 600;
    const left = window.screen.width / 2 - width / 2;
    const top = window.screen.height / 2 - height / 2;

    const windowFeatures = `scrollbars=no, resizable=no, copyhistory=no, width=${width}, height=${height}, top=${top}, left=${left}`;
    const popup = window.open(url, 'google-auth-popup', windowFeatures);
    return popup;
  };

  // Gestionnaire des messages de la popup
  const handlePopupMessage = useCallback(
    (event: MessageEvent) => {
      console.log('Received message from popup:', event.data);

      // Vérifier l'origine pour la sécurité
      if (event.origin !== window.location.origin) {
        console.log('Message origin mismatch:', event.origin, 'vs', window.location.origin);
        return;
      }

      const { type, error, redirectTo } = event.data;

      // Ne gérer que nos types de messages spécifiques
      if (type !== 'GOOGLE_AUTH_SUCCESS' && type !== 'GOOGLE_AUTH_ERROR') {
        return;
      }

      setPopup(null);

      if (type === 'GOOGLE_AUTH_ERROR') {
        setIsLoading(false);
        console.error('Authentication error from popup:', error);
        Swal.fire({
          icon: 'error',
          title: 'Erreur',
          text: `Échec de la connexion avec Google: ${error}`,
        });
        return;
      }

      if (type === 'GOOGLE_AUTH_SUCCESS') {
        setIsLoading(false);
        console.log('Authentication successful, redirecting to:', redirectTo || path);
        window.location.href = redirectTo || path;
        return;
      }
    },
    [setIsLoading, setPopup, path],
  );

  // Effet pour gérer la popup et les messages
  useEffect(() => {
    if (!popup) return;

    console.log('Setting up popup listeners');

    // Écouter les messages de la popup
    window.addEventListener('message', handlePopupMessage);

    // Vérifier si la popup est fermée manuellement
    const checkClosed = setInterval(() => {
      if (popup.closed) {
        console.log('Popup was closed manually');
        setIsLoading(false);
        setPopup(null);
        clearInterval(checkClosed);
      }
    }, 1000);

    return () => {
      console.log('Cleaning up popup listeners');
      window.removeEventListener('message', handlePopupMessage);
      clearInterval(checkClosed);
      setPopup(null);
    };
  }, [popup, handlePopupMessage]);

  return (
    <>
      {googleAuthEnabled ? (
        <div>
          <button
            type="button"
            onClick={handleGoogleSignIn}
            disabled={isLoading}
            className={cn(
              'flex gap-x-2 justify-center items-center p-3 border w-full border-gray-200 hover:bg-gray-50 hover:shadow-md hover:border-white transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed',
              className,
            )}
            {...rest}
          >
            <Image src="/images/auth/google.svg" alt="google" width={500} height={28} className="w-7 h-7" />
            <p>
              {t('loginWithGoogle')} {isLoading ? '...' : ''}
            </p>
          </button>
          <div className="flex items-center my-4">
            <div className="flex-grow h-px bg-gray-300" />
            <span className="px-4 text-sm text-gray-500">OR</span>
            <div className="flex-grow h-px bg-gray-300" />
          </div>
        </div>
      ) : (
        <></>
      )}
    </>
  );
}
