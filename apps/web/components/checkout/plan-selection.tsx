'use client';

import { sendGTMEvent } from '@next/third-parties/google';
import Image from 'next/image';
import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { useSupabase } from '@pdfily/supabase/hooks/use-supabase';
import { useLastEditFileId } from '@pdfily/documents/hooks/use-last-edit-file-id';
import { isGTMInitialized } from '@pdfily/shared/utils';
import { Button } from '@pdfily/ui/button';
import { cn } from '@pdfily/ui/utils';
import { useRouter } from '@/lib/i18n/navigation';
import TestimonialsSection from '../landing-page/testimonials-section';
import { useAuth } from '../session-provider';
import { usePostHogEvents } from '@/lib/tracking/posthog/usePostHogEvents';

const DocumentPreview = dynamic(() => import('./document-preview'), { ssr: false });

interface PlanSelectionProps {
  onSelectPlan: (plan: TPlan) => void;
}

export type TPlan = {
  id: number;
  name: string;
  price: string;
  total?: string;
  period: string;
  features: string[];
  disabled: string[];
  popular: boolean;
};

const featureIcons = new Map([
  ['editSave', '/images/checkout/EditAndSaveYourPdfs_mobile.svg'],
  ['annotateText', '/images/checkout/CommentHighlight_mobile.svg'],
  ['multiDevice', '/images/checkout/AccessFromAnyDevice_mobile.svg'],
  ['signOnline', '/images/checkout/SignYourDocsOnline_mobile.svg'],
  ['useTemplates', '/images/checkout/UseFormTemplates_mobile.svg'],
  ['addFields', '/images/checkout/AddNewFilableFields_mobile.svg'],
  ['createForms', '/images/checkout/CreateYourOwnForms_mobile.svg'],
]);

const plans: TPlan[] = [
  {
    id: 1,
    name: '7-day limited access',
    price: '$0.95',
    period: '',
    features: ['editSave', 'annotateText', 'multiDevice'],
    disabled: ['signOnline', 'useTemplates', 'addFields', 'createForms'],
    popular: false,
  },
  {
    id: 2,
    name: '7-day full access',
    price: '$1.95',
    period: '',
    features: ['editSave', 'annotateText', 'multiDevice', 'signOnline', 'useTemplates', 'addFields', 'createForms'],
    disabled: [],
    popular: true,
  },
  {
    id: 3,
    name: 'Annual plan',
    price: '$16.58',
    total: '$199',
    period: '/month',
    features: ['editSave', 'annotateText', 'multiDevice', 'signOnline', 'useTemplates', 'addFields', 'createForms'],
    disabled: [],
    popular: false,
  },
];

export default function PlanSelection({ onSelectPlan }: PlanSelectionProps) {
  const searchParams = useSearchParams();
  const supabase = useSupabase();
  const { trackPostHogPlanSelectionEvent } = usePostHogEvents();
  const t = useTranslations('Checkout.planSelection');
  const ft = useTranslations('Checkout.planSelection.features');
  const router = useRouter();
  const { setUser, setIsAuthenticated, setIsAnonymous } = useAuth();
  const { lastEditFileId } = useLastEditFileId();
  const previewDocumentId = searchParams.get('documentId') || lastEditFileId;

  useEffect(() => {
    const getUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (user) {
        setUser(user);
        setIsAuthenticated(!user.is_anonymous);
        setIsAnonymous(!!user.is_anonymous);
      }
    };
    getUser().then(() => router.refresh());
    if (!isGTMInitialized()) {
      console.warn('GTM not initialized on the payment details page: Event not sent');
      return;
    }

    sendGTMEvent({ event: 'checkout_plan_selection_reached' });
  }, []);

  const [clickedPlan, setClickedPlan] = useState<TPlan>(plans[1]!);
  const handlePlanClick = (plan: TPlan) => {
    trackPostHogPlanSelectionEvent({ ...plan });
    setClickedPlan(plan);
  };
  return (
    <div
      className={cn(
        'mt-12 flex w-full flex-col gap-20',
        'desktop:mt-[26px] desktop:gap-[26px]',
        'final:mt-[26px] final:gap-[26px]',
        'desktop:max-w-3xl',
      )}
    >
      {/* Desktop View */}
      <div className="flex desktop:hidden final:hidden gap-5 items-center ">
        {/* File Preview */}
        <div
          className={cn(
            'flex h-[600px] w-[510px] flex-col overflow-hidden rounded-2xl border border-[#E7E7E7] bg-[#F9F9F9]',
            'shadow-[2px_8px_20px_0px_#84899226]',
          )}
        >
          <div className="flex h-[66px] w-full items-center justify-center gap-3 bg-[#E9F9F4]">
            <Image
              src={'/images/checkout/CircleCheck_filled.svg'}
              alt={'Check'}
              width={26}
              height={26}
              className={cn('object-contain')}
            />
            <h3 className="font-onest text-[22px] font-medium leading-[26px] text-[#1FC794]">{t('documentReady')}</h3>
          </div>

          <div className="flex flex-1 items-start justify-center overflow-hidden p-6">
            <DocumentPreview documentId={previewDocumentId} />
          </div>
        </div>

        {/* Plans */}
        <div className="flex min-w-max ml-5 flex-col">
          <div className="mb-9 flex w-full items-center justify-between">
            {plans.map((plan, index) => (
              <div
                key={index}
                className={cn(
                  'flex flex-col overflow-hidden rounded-2xl',
                  'shadow-[1px_1px_1px_1px_#8DA0BC14] mx-2 min-h-[437px]',
                  plan.id === 1 ? 'w-[250px]' : 'w-[233px]',
                  clickedPlan?.id === plan.id ? 'border-2 border-[#1FC794]' : 'border border-[#E7E7E7]',
                )}
                onClick={() => handlePlanClick(plan)}
              >
                <div className="flex flex-col items-start p-5">
                  <h3 className="mb-4 font-onest text-xl font-medium leading-6 text-[#1C1C1C]">{plan.name}</h3>

                  <div className="mb-[18px] flex items-baseline">
                    <span className="font-onest text-[32px] font-bold leading-9 text-[#1C1C1C]">{plan.price}</span>
                    {!!plan.period && (
                      <span className="font-onest text-lg font-medium leading-9 text-[#585858]">{plan.period}</span>
                    )}
                  </div>

                  <div className="mb-[18px] h-px w-full bg-[#E7E7E7]"></div>

                  <ul className="mb-6 flex flex-col gap-3">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start gap-1.5">
                        <Image
                          src="/images/checkout/CircleCheck_outline.svg"
                          alt="Check"
                          width={18}
                          height={18}
                          className="object-contain"
                        />
                        <span className="font-onest text-sm font-normal leading-[18px] text-[#585858]">
                          {ft(feature)}
                        </span>
                      </li>
                    ))}
                    {plan.disabled.map((feature, idx) => (
                      <li key={idx} className="flex items-start gap-1.5">
                        <Image
                          src="/images/checkout/CircleX.svg"
                          alt="X"
                          width={18}
                          height={18}
                          className="object-contain"
                        />
                        <span className="font-onest text-sm font-normal leading-[18px] text-[#9B9A9A]">
                          {ft(feature)}
                        </span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    variant={clickedPlan?.id === plan.id ? 'default' : 'outline'}
                    className={cn(
                      'h-11 w-full rounded-[10px]',
                      'font font-onest text-[16px] font-medium leading-5',
                      clickedPlan?.id === plan.id
                        ? 'bg-[#1FC794] text-white hover:bg-emerald-600'
                        : 'border border-[#1FC794] text-[#1FC794] hover:bg-emerald-50 hover:text-[#1FC794]',
                    )}
                    onClick={() => onSelectPlan(plan)}
                  >
                    {t('continueButton')}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile View */}
      <div className={cn('hidden desktop:flex final:flex', 'w-full flex-col gap-y-6')}>
        {/* Plans */}
        <div className={cn('flex flex-col gap-2 w-full')}>
          {plans.map((plan, index) => (
            <div
              key={index}
              className={cn(
                'flex flex-col overflow-hidden rounded-2xl border',
                'shadow-[1px_1px_1px_1px_#8DA0BC14]',
                clickedPlan?.id === plan.id ? 'border-[#1FC794]' : 'border-[#E7E7E7]',
              )}
              onClick={() => handlePlanClick(plan)}
            >
              <div className="flex items-center justify-between px-4 py-[17px]" onClick={() => handlePlanClick(plan)}>
                <div className="flex items-center gap-4">
                  <div
                    className={cn(
                      'h-5 w-5 rounded-full',
                      clickedPlan?.name === plan.name ? 'border-[6px] border-[#1FC794]' : 'border border-[#E7E7E7]',
                    )}
                  ></div>
                  <h3 className="font-onest text-[16px] font-medium leading-5 text-[#1C1C1C]">{plan.name}</h3>
                </div>

                <div className="flex items-center">
                  <span className="font-onest text-lg font-bold leading-[22px] text-[#1C1C1C]">{plan.price}</span>
                  {!!plan.period && (
                    <span className="font-onest text-[16px] font-normal leading-[22px] text-[#585858]">
                      {plan.period}
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* File preview */}
        <div
          className={cn(
            'flex h-[344px] flex-col overflow-hidden rounded-2xl border border-[#E7E7E7] bg-[#F9F9F9] shadow-[2px_8px_20px_0px_#84899226]',
          )}
        >
          <div className="flex h-[54px] w-full items-center justify-center gap-1.5 bg-[#E9F9F4]">
            <Image
              src={'/images/checkout/CircleCheck_filled.svg'}
              alt={'Check'}
              width={20}
              height={20}
              className={cn('object-contain')}
            />
            <h3 className="font-onest text-[16px] font-medium leading-[26px] text-[#1FC794]">{t('documentReady')}</h3>
          </div>

          <DocumentPreview documentId={previewDocumentId} />
        </div>

        {/* Continue To checkout Button */}
        <Button
          className={cn(
            'h-11 w-full rounded-[10px]',
            'font font-onest text-[16px] font-medium leading-5',
            'bg-[#1FC794] text-white hover:bg-[#F0401D]/10',
          )}
          onClick={() => onSelectPlan(clickedPlan)}
        >
          {t('continueButton')}
        </Button>

        {/* Selected Plan details */}
        <div className={cn('flex flex-col gap-5')}>
          <div className="flex flex-col gap-4">
            <p className="font-onest text-lg font-medium leading-[22px]">{t('benefitsTitle')}</p>
            <ul className="flex flex-col gap-[14px]">
              {clickedPlan?.features.map((feature, idx) => (
                <li key={idx} className="flex items-center gap-[10px]">
                  <Image
                    src={featureIcons.get(feature) || ''}
                    alt="Check"
                    width={20}
                    height={20}
                    className="object-contain"
                  />
                  <span className="font-onest text-[16px] font-normal leading-[20px] text-[#585858]">
                    {ft(feature)}
                  </span>
                </li>
              ))}
            </ul>
          </div>
          <div className="h-px w-full bg-[#E7E7E7]"></div>

          <p className="text-center font-onest text-xs font-normal leading-[18px] text-[#585858]">
            {t.rich('infoText', {
              strong: (chunks) => <span className="text-[#1C1C1C]">{chunks}</span>,
              br: () => (
                <>
                  <br />
                </>
              ),
              a: (chunks) => (
                <a href="mailto:<EMAIL>" className="text-[#1C1C1C] underline">
                  {chunks}
                </a>
              ),
            })}
          </p>
        </div>
      </div>

      {/* General Testimonials section */}
      <div
        className={cn(
          'ml-[-100%] mr-[-100%] flex flex-col gap-8 bg-[#F9F9F9] py-20 pl-[100%] pr-[100%]',
          'desktop:py-10',
          'final:py-10',
        )}
      >
        <TestimonialsSection showTitle={false} />
      </div>
    </div>
  );
}
