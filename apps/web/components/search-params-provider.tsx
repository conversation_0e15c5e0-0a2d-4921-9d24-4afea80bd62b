'use client';

import { useEffect, useRef } from 'react';
import { useSetLocalPdfEditor } from '@pdfily/shared/hooks/useSetLocalPdfEditor';

/**
 * SearchParamsManager is a component that manages the search parameters for the client-side application.
 * It updates the session context with the search parameters.
 *
 * @returns null
 */
export default function SearchParamsProvider() {
  const hasMounted = useRef(false);
  const { setLocalPdfEditor } = useSetLocalPdfEditor();

  useEffect(() => {
    // Ensure setPaymentSystem is called once after mount
    if (!hasMounted.current) {
      hasMounted.current = true;

      // Set the search params in the local storage
      setLocalPdfEditor();
    }
  }, [setLocalPdfEditor]);

  return null;
}
