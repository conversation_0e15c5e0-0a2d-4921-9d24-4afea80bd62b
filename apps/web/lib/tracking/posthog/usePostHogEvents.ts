import { useCallback } from 'react';
import { usePostHogAnalytics } from './usePostHogAnalytics';
import { PostHogEventEnum } from '@pdfily/shared/utils';
import { PaymentIntent, SubscriptionType } from '@pdfily/payment';
import { Plan } from '@/app/[locale]/(site)/checkout/page';

/**
 * Interface for the PostHog purchase event payload.
 */
interface PostHogPurchaseEventPayload extends Partial<PaymentIntent> {
  isTrial: boolean;
  SubscriptionType: SubscriptionType;
}

/**
 * Interface for the PostHog payment failed event payload.
 */
interface PostHogPaymentFailedEventPayload {
  reason?: string;
}

/**
 * Interface for the PostHog payment failed event payload.
 */
interface PostHogSelectedPlanEventPayload extends Plan {
  SubscriptionType?: SubscriptionType;
}

/**
 * Custom hook for tracking PostHog events related to payments.
 * @returns Object with PostHog event tracking functions.
 */
export function usePostHogEvents() {
  const { captureEvent } = usePostHogAnalytics();

  /**
   * Track a purchase event in PostHog.
   * @param payload - The event payload including payment info, system, and version
   */
  const trackPostHogPurchaseEvent = useCallback(
    (payload: PostHogPurchaseEventPayload) => {
      captureEvent(PostHogEventEnum.PAYMENT_SUCCESS, payload);
    },
    [captureEvent],
  );

  /**
   * Track a payment failed event in PostHog.
   * @param payload - The event payload including payment info, system, and version
   */
  const trackPostHogPaymentFailedEvent = useCallback(
    (payload: PostHogPaymentFailedEventPayload) => {
      captureEvent(PostHogEventEnum.PAYMENT_FAILED, payload);
    },
    [captureEvent],
  );

  /**
   * Track an upload file event in PostHog.
   * @param payload - The event payload metadata
   */
  const trackPostHogUploadFileEvent = useCallback(
    (payload: any) => {
      captureEvent(PostHogEventEnum.UPLOADED_FILE, payload);
    },
    [captureEvent],
  );

  /**
   * Track a done button click event in PostHog.
   * @param payload - The event payload metadata
   */
  const trackPostHogDoneButtonEvent = useCallback(
    (payload: any) => {
      captureEvent(PostHogEventEnum.CLICKED_DONE_BUTTON, payload);
    },
    [captureEvent],
  );

  /**
   * Track a selected planevent in PostHog.
   * @param payload - The event payload metadata
   */
  const trackPostHogPlanSelectionEvent = useCallback(
    (payload: PostHogSelectedPlanEventPayload) => {
      captureEvent(PostHogEventEnum.SELECTED_PLAN, payload);
    },
    [captureEvent],
  );

  // Add more event trackers here as needed

  return {
    trackPostHogPaymentFailedEvent,
    trackPostHogPurchaseEvent,
    trackPostHogUploadFileEvent,
    trackPostHogDoneButtonEvent,
    trackPostHogPlanSelectionEvent,
  };
}
