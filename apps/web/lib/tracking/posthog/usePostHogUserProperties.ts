'use client';

import { useEffect } from 'react';
import { usePostHogAnalytics } from './usePostHogAnalytics';

/**
 * Hook that monitors changes in use properties
 * and automatically updates the PostHog properties
 */
export const usePostHogUserProperties = () => {
  const { updateUserProperties } = usePostHogAnalytics();

  useEffect(() => {
    updateUserProperties({});
  }, [updateUserProperties]);

  return { updateUserProperties };
};
