{"name": "@pdfily/ui", "version": "0.1.0", "description": "UI component library for the PDFily monorepo.", "type": "module", "sideEffects": ["**/*.css"], "files": ["dist"], "license": "MIT", "scripts": {"build": "npm run build:tsc && npm run build:tailwind", "build:tsc": "tsc", "build:watch": "tsc --watch", "build:tailwind": "tailwindcss -i ./styles/globals.css -o ./dist/index.css", "clean": "git clean -xdf .turbo node_modules", "check-types": "tsc --noEmit", "dev": "tailwindcss -i ./styles/globals.css -o ./dist/index.css --watch", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint src --max-warnings 0", "typecheck": "tsc --noEmit"}, "peerDependencies": {"react": "^19", "react-dom": "^19"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@pdfily/eslint-config": "workspace:*", "@pdfily/prettier-config": "workspace:*", "@pdfily/tailwind-config": "workspace:*", "@pdfily/typescript-config": "workspace:*", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "5.8.3"}, "exports": {"./badge": "./src/components/badge.tsx", "./button": "./src/components/button.tsx", "./card": "./src/components/card.tsx", "./carousel": "./src/components/carousel.tsx", "./checkbox": "./src/components/checkbox.tsx", "./dialog": "./src/components/dialog.tsx", "./dropdown-menu": "./src/components/dropdown-menu.tsx", "./input": "./src/components/input.tsx", "./label": "./src/components/label.tsx", "./select": "./src/components/select.tsx", "./progress": "./src/components/progress.tsx", "./spinner": "./src/components/spinner.tsx", "./tabs": "./src/components/tabs.tsx", "./textarea": "./src/components/textarea.tsx", "./utils": "./src/lib/utils/index.ts", "./hooks/*": "./src/hooks/*.ts", "./if": "./src/pdfkit/if.tsx"}, "turbo": {"pipeline": {"build": {"outputs": []}}}, "typesVersions": {"*": {"*": ["src/*"]}}, "prettier": "@pdfily/prettier-config"}