'use client';

import { useCallback } from 'react';
import { useSupabase } from './use-supabase';
import { createPopupAuthService } from '../popup-auth.service';

/**
 * @name usePopupAuth
 * @description Hook for handling popup-based OAuth authentication with proper PKCE flow
 * @returns Object with methods for handling popup authentication
 */
export function usePopupAuth() {
  const supabase = useSupabase();

  /**
   * @name exchangeCodeForSession
   * @description Exchanges an authorization code for a session using client-side PKCE flow
   * This should be called from the main window after receiving the code from the popup
   *
   * @param {string} authCode - The authorization code from OAuth callback
   * @param {string} [anonymousUserId] - Optional anonymous user ID for document transfer
   * @returns {Promise<{ success: boolean; error?: string; user?: any }>}
   */
  const exchangeCodeForSession = useCallback(
    async (authCode: string, anonymousUserId?: string) => {
      try {
        // Create popup auth service with browser client (has PKCE state)
        const authService = createPopupAuthService(supabase);

        // Use the popup-specific method that handles PKCE properly
        const result = await authService.exchangeCodeForSession(authCode, anonymousUserId);

        return result;
      } catch (error) {
        console.error('Error in popup auth exchange:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error occurred',
        };
      }
    },
    [supabase],
  );

  return {
    exchangeCodeForSession,
  };
}
