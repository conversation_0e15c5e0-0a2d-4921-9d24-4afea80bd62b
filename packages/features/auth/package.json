{"name": "@pdfily/auth", "private": true, "version": "0.1.0", "description": "Feature package for authentication in the PDFily monorepo.", "type": "module", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "dependencies": {"@marsidev/react-turnstile": "^1.1.0", "@pdfily/config": "workspace:*", "@pdfily/supabase": "workspace:*", "next": "^15.4.4", "react": "^19.1.0", "sweetalert2": "^11.22.2"}, "devDependencies": {"@pdfily/eslint-config": "workspace:*", "@pdfily/prettier-config": "workspace:*", "@pdfily/typescript-config": "workspace:*", "@types/node": "^24.1.0", "@types/react": "^19.1.8"}, "exports": {"./captcha/client": "./src/captcha/client/index.ts", "./signin": "./src/signin/index.ts"}, "turbo": {"pipeline": {"build": {"outputs": []}}}, "typesVersions": {"*": {"*": ["src/*"]}}, "prettier": "@pdfily/prettier-config"}