import { ToolbarItem } from '@nutrient-sdk/viewer';

/**
 * Returns the customized toolbar items array for Nutrient/PSPDFKit Viewer.
 * This toolbar includes sidebar-related items and annotation tools.
 *
 * @returns {Array<ToolbarItem>} Array of toolbar item configurations.
 *
 * @example
 * const toolbarItems = getCustomizedToolbarItems();
 */
export async function getCustomizedToolbarItems(): Promise<Array<ToolbarItem>> {
  return [
    // Sidebar
    {
      type: 'sidebar-thumbnails',
      id: 'sidebar-thumbnails',
      title: 'Thumbnails',
      icon: '/images/pspdfkit/sidebar-thumbnails.svg',
    },
    // Undo/Redo
    {
      type: 'undo',
      id: 'undo',
      title: 'Undo',
      icon: '/images/pspdfkit/undo.svg',
    },
    {
      type: 'redo',
      id: 'redo',
      title: 'Redo',
      icon: '/images/pspdfkit/redo.svg',
    },
    {
      type: 'sidebar-annotations',
      id: 'sidebar-annotations',
      title: 'Annotations',
      icon: '/images/pspdfkit/sidebar-annotations.svg',
    },
    {
      type: 'sidebar-document-outline',
      id: 'sidebar-document-outline',
      title: 'Outline',
      icon: '/images/pspdfkit/sidebar-document-outline.svg',
    },
    {
      type: 'sidebar-bookmarks',
      id: 'sidebar-bookmarks',
      title: 'Bookmarks',
      icon: '/images/pspdfkit/sidebar-bookmarks.svg',
    },

    // Text
    {
      type: 'text',
      id: 'text',
      title: 'Add Text',
      icon: '/images/pspdfkit/text.svg',
    },
    {
      type: 'content-editor',
      id: 'content-editor',
      title: 'Edit Text',
      icon: '/images/pspdfkit/edit-text.svg',
    },
    {
      type: 'signature',
      id: 'signature',
      title: 'Sign Document',
      icon: '/images/pspdfkit/signature.svg',
    },

    // Shapes
    {
      type: 'line',
      id: 'line',
      title: 'Line',
      icon: '/images/pspdfkit/line.svg',
    },
    {
      type: 'arrow',
      id: 'arrow',
      title: 'Arrow',
      icon: '/images/pspdfkit/arrow.svg',
    },
    {
      type: 'rectangle',
      id: 'rectangle',
      title: 'Box',
      icon: '/images/pspdfkit/rectangle.svg',
    },
    {
      type: 'ellipse',
      id: 'ellipse',
      title: 'Circle',
      icon: '/images/pspdfkit/ellipse.svg',
    },
    {
      type: 'polygon',
      id: 'polygon',
      title: 'Polygon',
      icon: '/images/pspdfkit/polygon.svg',
    },

    // Ink
    {
      type: 'ink',
      id: 'ink',
      title: 'Draw',
      icon: '/images/pspdfkit/ink.svg',
      dropdownGroup: 'ink',
    },
    {
      type: 'ink-eraser',
      id: 'ink-eraser',
      title: 'Eraser',
      icon: '/images/pspdfkit/ink-eraser.svg',
      dropdownGroup: 'ink-eraser',
    },
    {
      type: 'highlighter',
      id: 'highlighter',
      title: 'Highlight',
      icon: '/images/pspdfkit/highlighter.svg',
      dropdownGroup: 'highlighter',
    },
    {
      type: 'text-highlighter',
      id: 'text-highlighter',
      title: 'Text highlight',
      icon: '/images/pspdfkit/text-highlighter.svg',
      dropdownGroup: 'text-highlighter',
    },

    // Image & Stamp
    {
      type: 'image',
      id: 'image',
      title: 'Image',
      icon: '/images/pspdfkit/image.svg',
      dropdownGroup: 'image',
    },
    {
      type: 'stamp',
      id: 'stamp',
      title: 'Stamp',
      icon: '/images/pspdfkit/stamp.svg',
      dropdownGroup: 'stamp',
    },

    // Link & Note
    {
      type: 'link',
      id: 'link',
      title: 'Link',
      icon: '/images/pspdfkit/link.svg',
      dropdownGroup: 'link',
    },
    {
      type: 'note',
      id: 'note',
      title: 'Note',
      icon: '/images/pspdfkit/note.svg',
      dropdownGroup: 'note',
    },

    // Separator
    { type: 'spacer' },

    // Document
    {
      type: 'document-editor',
      id: 'document-editor',
      title: 'Document editor',
      icon: '/images/pspdfkit/document-editor.svg',
    },
    {
      type: 'print',
      id: 'print',
      title: 'Print',
      icon: '/images/pspdfkit/print.svg',
    },
    {
      type: 'search',
      id: 'search',
      title: 'Search',
      icon: '/images/pspdfkit/search.svg',
    },

    // Navigator
    /* { type: 'pager', dropdownGroup: 'navigator' },
    { type: 'zoom-in', dropdownGroup: 'navigator' },
    { type: 'zoom-out', dropdownGroup: 'navigator' },
    { type: 'pan', dropdownGroup: 'navigator' },
    { type: 'zoom-mode', dropdownGroup: 'navigator' }, */

    // Sidebar
    /* { type: 'sidebar-signatures', dropdownGroup: 'hidden-sidebar' },
    { type: 'sidebar-attachments', dropdownGroup: 'hidden-sidebar' },
    { type: 'sidebar-layers', dropdownGroup: 'hidden-sidebar' }, */

    // Shapes
    /* { type: 'polyline', dropdownGroup: 'hidden-shapes' },
    { type: 'polygon-area', dropdownGroup: 'hidden-shapes' },
    { type: 'cloudy-rectangle', dropdownGroup: 'hidden-shapes' },
    { type: 'cloudy-ellipse', dropdownGroup: 'hidden-shapes' },
    { type: 'cloudy-polygon', dropdownGroup: 'hidden-shapes' },
    { type: 'dashed-rectangle', dropdownGroup: 'hidden-shapes' },
    { type: 'dashed-ellipse', dropdownGroup: 'hidden-shapes' },
    { type: 'dashed-polygon', dropdownGroup: 'hidden-shapes' },
    { type: 'rectangle-area', dropdownGroup: 'hidden-shapes' },
    { type: 'ellipse-area', dropdownGroup: 'hidden-shapes' },
    { type: 'polygon-area', dropdownGroup: 'hidden-shapes' },
    { type: 'redact-rectangle', dropdownGroup: 'hidden-shapes' }, */

    // Others
    /* { type: 'redact-text-highlighter', dropdownGroup: 'others' },
    { type: 'distance', dropdownGroup: 'others' },
    { type: 'comment', dropdownGroup: 'others' },
    { type: 'measure', dropdownGroup: 'others' },
    { type: 'callout', dropdownGroup: 'others' },

    { type: 'document-crop', dropdownGroup: 'others' },
    { type: 'export-pdf', dropdownGroup: 'others' },
    { type: 'document-comparison', dropdownGroup: 'others' },
    { type: 'multi-annotations-selection', dropdownGroup: 'others' },
    { type: 'form-creator', dropdownGroup: 'others' },
    { type: 'ai-assistant', dropdownGroup: 'others' },

    { type: 'responsive-group', dropdownGroup: 'others' },

    { type: 'marquee-zoom', dropdownGroup: 'others' },
    { type: 'linearized-download-indicator', dropdownGroup: 'others' },
    { type: 'debug', dropdownGroup: 'others' },
    { type: 'annotate', dropdownGroup: 'others' }, */

    // N/A
    /* { type: 'layout-config', dropdownGroup: 'others' }, */
  ];
}
