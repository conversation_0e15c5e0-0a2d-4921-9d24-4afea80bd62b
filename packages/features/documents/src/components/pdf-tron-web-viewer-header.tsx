'use client';

import Image from 'next/image';
import type { User } from '@supabase/supabase-js';
import type { WebViewerInstance } from '@pdftron/webviewer';
import { useCustomDownloadButton } from '../hooks/apryse-viewer/toolbars/use-custom-download-button';

interface PdfTronWebViewerHeaderProps {
  instance: WebViewerInstance | null;
  source: string | null;
  documentId: string | null | undefined;
  user: User;
}

export default function PdfTronWebViewerHeader({ instance, source, documentId, user }: PdfTronWebViewerHeaderProps) {
  const customDownloadButton: any = useCustomDownloadButton(instance, source, documentId, user);

  return (
    <div className="p-4 md:px-20 md:py-2.5 border-b border-white bg-white flex justify-between items-center md:gap-3">
      <Image
        src="/images/header/Logo.svg"
        alt="PDFily Logo"
        width={121}
        height={36}
        priority
        className="w-[121px] h-auto"
      />
      {customDownloadButton && (
        <button
          onClick={customDownloadButton.onClick}
          style={{
            color: '#F0401D',
            border: '1px solid #F0401D',
            borderRadius: '10px',
            padding: '10px 20px',
            background: 'white',
            fontWeight: 500,
            fontSize: '16px',
            lineHeight: '20px',
          }}
        >
          Done
        </button>
      )}
    </div>
  );
}
