'use client';

import type { User } from '@supabase/supabase-js';
import { If } from '@pdfily/ui/if';
import { useLocalPdfEditor } from '@pdfily/shared/store/hooks/useLocalPdfEditor';
import PDFTronWebViewer from './pdf-tron-web-viewer';
import NutrientWebViewer from './pdf-nutrient-web-viewer';
import { UseViewerKitProps } from '../types/viewer-kit.types';

interface PDFViewerRendererContainerProps extends UseViewerKitProps {
  user: User;
}

/**
 * PDFViewerRendererContainer is a wrapper component that renders both
 * the Nutrient and PDFTron viewers within a full-screen layout.
 * It also enables a confirmation prompt when the user tries to leave the page.
 *
 * @param {UseViewerKitProps} props - Viewer props including source, documentId, and optional locale.
 * @param {string | ArrayBuffer | null} props.source - The document source (URL or binary).
 * @param {string | null} props.documentId - Unique document identifier (if any).
 * @param {string} [props.locale] - Optional locale/language setting.
 * @param {User} props.user - The user object from Supabase.
 */
export default function PDFViewerRendererContainer({
  source,
  documentId,
  locale,
  user,
}: PDFViewerRendererContainerProps) {
  const { viewerType } = useLocalPdfEditor();

  // TODO : Activate the usePageConfirmation only if the redirection action did not come from the Signin with google button
  // useLeavePageConfirmation(true, fromGoogleAuth);

  return (
    <div className="relative w-full">
      <If condition={viewerType === 'nutrient'}>
        <NutrientWebViewer source={source} documentId={documentId} locale={locale} user={user} />
      </If>

      <If condition={viewerType === 'pdftron'}>
        <PDFTronWebViewer source={source} documentId={documentId} locale={locale} user={user} />
      </If>

      <If condition={!viewerType}>
        <div className="flex items-center justify-center h-full text-red-500">
          <p>❌ No PDF editor configured. Please set NEXT_PUBLIC_PDF_EDITOR in your environment.</p>
        </div>
      </If>
    </div>
  );
}
