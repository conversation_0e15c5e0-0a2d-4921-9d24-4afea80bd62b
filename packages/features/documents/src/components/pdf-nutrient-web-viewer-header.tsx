'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import type { User } from '@supabase/supabase-js';
import { useCustomDownloadButton } from '../hooks/nutrient-viewer/toolbars/use-custom-download-button';
import { Instance, ViewState } from '@nutrient-sdk/viewer';

interface PdfNutrientWebViewerHeaderProps {
  instance: Instance | null;
  documentId: string | null | undefined;
  user: User;
}

// List of interaction modes that should disable the button
const DISABLE_MODES = ['DOCUMENT_EDITOR', 'CONTENT_EDITOR', 'TEXT', 'SIGNATURE', 'STAMP_PICKER', 'STAMP_CUSTOM'];

export default function PdfNutrientWebViewerHeader({ instance, documentId, user }: PdfNutrientWebViewerHeaderProps) {
  const customDownloadButton = useCustomDownloadButton(instance, documentId, user);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (!instance) return;

    const handleViewStateChange = (viewState: ViewState) => {
      const mode = viewState.interactionMode;
      // Some SDKs may use uppercase, some lowercase, so normalize
      setIsEditing(typeof mode === 'string' && DISABLE_MODES.includes(mode.toUpperCase()));
    };

    instance.addEventListener('viewState.change', handleViewStateChange);
    // Set initial state
    handleViewStateChange(instance.viewState);

    return () => {
      instance.removeEventListener('viewState.change', handleViewStateChange);
    };
  }, [instance]);

  return (
    <div className="px-8 small:px-4 py-2 border-b border-white bg-white flex justify-between items-center md:gap-3">
      <Image
        src="/images/header/Logo.svg"
        alt="PDFily Logo"
        width={121}
        height={36}
        priority
        className="w-[121px] h-auto"
      />
      {customDownloadButton && (
        <button
          onClick={customDownloadButton.onPress}
          disabled={isEditing}
          style={{
            color: '#F0401D',
            border: '1px solid #F0401D',
            borderRadius: '10px',
            padding: '10px 30px',
            background: 'white',
            fontWeight: 500,
            fontSize: '16px',
            lineHeight: '20px',
            opacity: isEditing ? 0.5 : 1,
            cursor: isEditing ? 'not-allowed' : 'pointer',
          }}
        >
          Done
        </button>
      )}
    </div>
  );
}
