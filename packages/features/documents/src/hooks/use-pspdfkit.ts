import { useEffect, useState } from 'react';
import { Instance, ToolbarItem } from '@nutrient-sdk/viewer';
import appConfig from '@pdfily/config/app.config';
import { usePDFViewerBase } from './use-pdf-viewer-base';
import { getInitialViewState } from '../lib/nutrient-viewer/initial-view-state';
import { getCustomizedToolbarItems } from '../lib/nutrient-viewer/toolbar-items';
import { UseViewerKitProps, UsePSPDFKitResponse } from '../types/viewer-kit.types';

// Exclude undo and redo on mobile
const EXCLUDED_ON_MOBILE = new Set(['undo', 'redo']);

/**
 * Retrieves the toolbar items for the Nutrient Viewer.
 *
 * @param isDesktopMode - Boolean indicating if we're in desktop mode.
 * @returns Promise of toolbar items.
 */
export async function getToolbarItems(isDesktopMode: boolean): Promise<ToolbarItem[]> {
  const items = await getCustomizedToolbarItems();
  if (!isDesktopMode) {
    return items.filter((item) => !EXCLUDED_ON_MOBILE.has(item.id ?? ''));
  }
  return items;
}

/**
 * Loads PSPDFKit/Nutrient Viewer into the container.
 *
 * @param container - HTML div where the viewer will be rendered.
 * @param document - Document URL or binary.
 * @param isDesktopMode - Boolean indicating if we're in desktop mode.
 * @returns Promise of PSPDFKit instance or undefined.
 */
export const loadPSPDFKit = async (
  container: HTMLDivElement | null,
  document: string | ArrayBuffer,
  locale: string = 'en',
  isDesktopMode: boolean = true,
): Promise<Instance | undefined> => {
  if (!container || typeof window === 'undefined' || !window.NutrientViewer) {
    console.warn('Cannot load PSPDFKit: Invalid container or NutrientViewer not available');
    return undefined;
  }

  // NutrientViewer is the global variable that contains the Nutrient Viewer instance.
  const NutrientViewer = window.NutrientViewer;

  const initialViewState = getInitialViewState(NutrientViewer);
  const origin = window.location.origin;
  const styleBasePath = `${origin}/styles/pspdfkit`;
  const toolbarItems = await getToolbarItems(isDesktopMode);

  try {
    NutrientViewer.Options.ANNOTATION_TOOLBAR_RESPONSIVE_BREAKPOINT = 20;

    // Unload the container before loading to avoid double-mount
    NutrientViewer.unload(container);

    const start = performance.now();

    // Load the Nutrient Viewer instance.
    const instance = await NutrientViewer.load({
      ...(appConfig.isNutrientLicensedEnabled ? { licenseKey: appConfig.nutrientLicenceKey } : {}),
      locale,
      container,
      document,
      initialViewState,
      toolbarItems,
      baseUrl: `${origin}/lib/nutrient-viewer/`,
      styleSheets: [
        `${styleBasePath}/global.css`,
        `${styleBasePath}/custom.css`,
        `${styleBasePath}/tool-bar.css`,
        `${styleBasePath}/side-bar.css`,
        `${styleBasePath}/viewport.css`,
        `${styleBasePath}/annotation-tool-bar.css`,
      ],
      enableClipboardActions: true,
      enableHistory: true,
      toolbarPlacement: isDesktopMode ? NutrientViewer.ToolbarPlacement.TOP : NutrientViewer.ToolbarPlacement.BOTTOM,
      customUI: {
        [NutrientViewer.UIElement.Sidebar]: {
          [NutrientViewer.SidebarMode.ANNOTATIONS]: ({ containerNode }) => {
            // Create the custom delete button only once
            const customDeleteButton = window.document.createElement('button');
            // Add event listener to the custom delete button
            customDeleteButton.addEventListener('click', () => {
              const deleteButton = (containerNode as HTMLDivElement).querySelector<HTMLButtonElement>(
                '.PSPDFKit-Sidebar-Annotations-Delete',
              );
              if (deleteButton) deleteButton?.click();
            });

            return {
              node: containerNode,
              onRenderItem: ({ itemContainerNode }) => {
                const lastChild = itemContainerNode.lastChild;
                if (lastChild?.nodeName === 'DIV') {
                  itemContainerNode.appendChild(customDeleteButton);
                }
              },
            };
          },
        },
      },
    });

    const end = performance.now();
    console.log('NutrientViewer loaded in', (end - start).toFixed(2), 'ms');

    return instance;
  } catch (error) {
    console.log({ container, document, locale, isDesktopMode });
    console.error('Error while loading NutrientViewer instance:', error);
  }
};

// Utility to detect if it's desktop
const isDesktop = (): boolean => {
  if (typeof window === 'undefined') return true;
  return window.innerWidth >= 768;
};

/**
 * React hook to initialize and manage PSPDFKit viewer.
 */
const usePSPDFKit = ({ source, locale, setIsUndoRedo }: UseViewerKitProps): UsePSPDFKitResponse => {
  const { viewer, instance, setInstance, loading, setLoading } = usePDFViewerBase<Instance | null>();
  const [desktopMode, setDesktopMode] = useState(isDesktop());

  // Watch for resize events to detect desktop/mobile mode
  useEffect(() => {
    const handleResize = () => {
      setDesktopMode(isDesktop());
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    setLoading(true);
    const container = viewer.current;

    if (!source || !container) {
      setLoading(false);
      return;
    }

    loadPSPDFKit(container, source, locale, desktopMode)
      .then((instance) => {
        if (instance) {
          instance.addEventListener('history.willChange', () => {
            setIsUndoRedo?.((prev) => !prev);
          });
          // Remove the 'Save as' button from the Document Editor
          // This prevents users from saving modified files without going through the subscription flow
          instance.setDocumentEditorFooterItems((items) => items.filter((item) => item.type !== 'save-as'));
          setInstance(instance);
          console.log('PSPDFKit was initialized successfully.');
        }
      })
      .finally(() => {
        setLoading(false);
      });

    return () => {
      if (container && window.NutrientViewer) {
        window.NutrientViewer.unload(container);
      }
    };
  }, [source, desktopMode]);

  return { viewer, instance, loading };
};

export default usePSPDFKit;
