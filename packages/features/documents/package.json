{"name": "@pdfily/documents", "private": true, "version": "0.1.0", "description": "Feature package for document management and PDF viewing in the PDFily monorepo.", "type": "module", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "dependencies": {"@nutrient-sdk/viewer": "1.3.0", "@pdfily/auth": "workspace:*", "@pdfily/config": "workspace:*", "@pdfily/shared": "workspace:*", "@pdfily/supabase": "workspace:*", "@pdfily/ui": "workspace:*", "@pdftron/webviewer": "^11.6.1", "@supabase/supabase-js": "2.49.8", "@tanstack/react-query": "^5.83.0", "next": "^15.4.4", "react": "^19.1.0", "sweetalert2": "^11.22.2"}, "devDependencies": {"@pdfily/eslint-config": "workspace:*", "@pdfily/prettier-config": "workspace:*", "@pdfily/typescript-config": "workspace:*", "@types/node": "^24.1.0", "@types/react": "^19.1.8"}, "exports": {"./components": "./src/components/index.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts", "./api": "./src/server/api.ts"}, "turbo": {"pipeline": {"build": {"outputs": []}}}, "typesVersions": {"*": {"*": ["src/*"]}}, "prettier": "@pdfily/prettier-config"}