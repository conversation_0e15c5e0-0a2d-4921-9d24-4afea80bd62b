{"name": "@pdfily/shared", "private": true, "version": "0.1.0", "description": "Shared utilities, hooks, and types for the PDFily monorepo.", "type": "module", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "dependencies": {"generate-password": "^1.7.1", "lscache": "^1.3.2", "next": "^15.4.4", "pdf-lib": "^1.17.1", "react": "^19.1.0"}, "devDependencies": {"@pdfily/config": "workspace:*", "@pdfily/eslint-config": "workspace:*", "@pdfily/prettier-config": "workspace:*", "@pdfily/typescript-config": "workspace:*", "@types/lscache": "^1.3.4", "@types/node": "^24.1.0", "@types/react": "^19.1.8"}, "exports": {"./hooks": "./src/hooks/index.ts", "./hooks/*": "./src/hooks/*.ts", "./store/hooks/*": "./src/store/hooks/*.ts", "./utils": "./src/utils/index.ts", "./utils/*": "./src/utils/*.ts"}, "turbo": {"pipeline": {"build": {"outputs": []}}}, "typesVersions": {"*": {"*": ["src/*"]}}, "prettier": "@pdfily/prettier-config"}