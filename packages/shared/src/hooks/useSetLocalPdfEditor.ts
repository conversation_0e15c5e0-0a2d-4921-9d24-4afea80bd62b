import { useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { PdfEditor } from '@pdfily/config/app.config';
import { useLocalPdfEditor } from '../store/hooks/useLocalPdfEditor';

export function useSetLocalPdfEditor() {
  const searchParams = useSearchParams();
  const { updateLocalPdfEditor } = useLocalPdfEditor();

  // Set utm_campaign and utm_id from URL parameters
  const setLocalPdfEditor = useCallback(() => {
    const editor = searchParams.get('editor');

    // Only allow 'pdftron' or 'nutrient' as valid editors
    if (editor === 'pdftron' || editor === 'nutrient') {
      updateLocalPdfEditor(editor as PdfEditor);
    }
  }, [searchParams, updateLocalPdfEditor]);

  return { setLocalPdfEditor };
}
