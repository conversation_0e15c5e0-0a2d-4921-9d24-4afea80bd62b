'use client';

import { useEffect } from 'react';

/**
 * Hook to show browser's "Leave site?" confirmation when user tries to close/refresh/navigate.
 * @param enabled Boolean flag to enable/disable this behavior.
 * @param bypassFromGoogleAuth Boolean flag to bypass confirmation when coming from Google auth.
 */
export const useLeavePageConfirmation = (enabled: boolean = true, bypassFromGoogleAuth: boolean = false) => {
  useEffect(() => {
    // Disable confirmation if coming from Google auth or if explicitly disabled
    if (!enabled || bypassFromGoogleAuth) return;

    // Handle Before
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      event.preventDefault();
      event.returnValue = '';
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [enabled, bypassFromGoogleAuth]);
};
