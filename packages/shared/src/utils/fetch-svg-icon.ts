// utils/fetch-svg-icon.ts

// Cache simple pour éviter les requêtes répétées
const svgCache = new Map<string, Promise<string>>();

/**
 * Fetches an SVG icon as a string from the specified path.
 *
 * @param path - The URL or relative path to the SVG file.
 * @returns A promise that resolves to the SVG content as a string, or an empty string if the fetch fails.
 *
 * @example
 * const svg = await fetchSvgIcon('/icons/my-icon.svg');
 * element.innerHTML = svg;
 */
export async function fetchSvgIcon(path: string): Promise<string> {
  // Vérifier le cache d'abord
  if (svgCache.has(path)) {
    return svgCache.get(path)!;
  }

  // Créer la promesse et la mettre en cache immédiatement
  const promise = (async () => {
    try {
      const response = await fetch(path);
      return await response.text();
    } catch (error) {
      console.error(`❌ Error fetching SVG icon at ${path}:`, error);
      return '';
    }
  })();

  svgCache.set(path, promise);
  return promise;
}

/**
 * Précharge une liste d'icônes SVG en arrière-plan
 * @param paths - Liste des chemins SVG à précharger
 */
export function preloadSvgIcons(paths: string[]): void {
  paths.forEach((path) => {
    if (!svgCache.has(path)) {
      fetchSvgIcon(path); // Lance le chargement sans attendre
    }
  });
}
