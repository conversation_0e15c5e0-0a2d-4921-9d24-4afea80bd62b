import lscache from 'lscache';
import type { Dispatch, SetStateAction } from 'react';

/**
 * Updates a value in both React state and lscache (localStorage with expiration).
 *
 * @param contextPrefix - Prefix for the storage key (e.g., module or feature name)
 * @param variableName - Name of the variable to store
 * @param newVariable - New value to set (string or number)
 * @param setter - React state setter function
 * @returns {string} Success message if update succeeded, error message otherwise
 */
export function update({
  contextPrefix,
  variableName,
  newVariable,
  setter,
}: {
  contextPrefix: string;
  variableName: string;
  newVariable: string | number;
  setter: Dispatch<SetStateAction<string | number | any>>;
}): string {
  setter(newVariable);
  const success = lscache.set('' + contextPrefix + '-' + variableName, newVariable, 525601); // ~1 year in minutes
  return success
    ? `Prop: ${variableName} successfully updated.`
    : `Failure during update for prop called: ${variableName}.`;
}
