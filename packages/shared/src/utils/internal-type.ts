import { InternalFileType } from '../types/internal-type';

/**
 * Determines the internal file type based on the file extension.
 *
 * @param {string} fileName - The name of the file.
 * @returns {InternalFileType} - The determined file type.
 */
export function getInternalFileType(fileName: string) {
  const extension = fileName.split('.').pop()?.toLowerCase();

  switch (extension) {
    case 'pdf':
      return InternalFileType.PDF;
    case 'doc':
      return InternalFileType.DOC;
    case 'docx':
      return InternalFileType.DOCX;
    case 'xls':
      return InternalFileType.XLS;
    case 'xlsx':
      return InternalFileType.XLSX;
    case 'txt':
      return InternalFileType.TXT;
    case 'csv':
      return InternalFileType.CSV;
    case 'ppt':
      return InternalFileType.PPT;
    case 'pptx':
      return InternalFileType.PPTX;
    case 'jpeg':
    case 'jpg':
      return InternalFileType.JPG;
    case 'png':
      return InternalFileType.PNG;
    case 'gif':
      return InternalFileType.GIF;
    case 'zip':
      return InternalFileType.ZIP;
    case 'rar':
      return InternalFileType.RAR;
  }
}

export enum PostHogEventEnum {
  UPLOADED_FILE = 'Uploaded file',
  CLICKED_DONE_BUTTON = 'Clicked done button',
  SELECTED_PLAN = 'Selected plan',
  STARTED_CHECKOUT = 'Started checkout',
  PAYMENT_SUCCESS = 'Payment success',
  PAYMENT_FAILED = 'Payment failed',
  DOWNLOAD_FILE = 'Download file',
}
