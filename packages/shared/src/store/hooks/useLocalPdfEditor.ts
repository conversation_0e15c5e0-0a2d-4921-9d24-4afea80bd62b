import { useCallback, useState } from 'react';
import lscache from 'lscache';
import appConfig from '@pdfily/config/app.config';
import { PdfEditor } from '../../hooks/useSetLocalPdfEditor';
import { update } from '../../utils/lscache-utils';

/**
 * Custom hook to manage the selected PDF editor in localStorage and React state.
 * Uses lscache for expiration and persists the editor selection across sessions.
 */
export function useLocalPdfEditor() {
  // Initialize state from localStorage or fallback to config default
  const [viewerType, setViewerType] = useState<PdfEditor>(
    (lscache.get('ls-viewer:type') as PdfEditor) ?? appConfig.pdfEditor,
  );

  /**
   * Update the PDF editor in both state and localStorage
   * @param newPdfEditor - The new editor to set
   */
  const updateLocalPdfEditor = useCallback((newPdfEditor: PdfEditor) => {
    update({
      contextPrefix: 'ls',
      variableName: 'viewer:type',
      newVariable: newPdfEditor,
      setter: setViewerType,
    });
  }, []);

  return {
    viewerType,
    setViewerType,
    updateLocalPdfEditor,
  };
}
