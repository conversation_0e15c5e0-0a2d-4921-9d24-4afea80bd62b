# Turborepo Tailwind CSS Starter Kit

This is a Next.js project bootstrapped with `npx create-turbo@latest -e with-tailwind`, configured with Turborepo and Tailwind CSS. Based on the [with-tailwind example](https://github.com/vercel/turborepo/tree/main/examples/with-tailwind).

## What's inside?

This Turborepo includes the following packages/apps:

### Apps and Packages

- `docs`: a [Next.js](https://nextjs.org/) app with [Tailwind CSS](https://tailwindcss.com/)
- `web`: another [Next.js](https://nextjs.org/) app with [Tailwind CSS](https://tailwindcss.com/)
- `ui`: a stub React component library with [Tail<PERSON> CSS](https://tailwindcss.com/) shared by both `web` and `docs` applications

## Getting Started

### Prerequisites

- Node.js 20.x or later (preferably the latest LTS version)
- PNPM

### Installation

#### 1. Clone the repository

```bash
git clone https://github.com/Robust-Digital/pdfily.git
```

#### 2. Install dependencies

```bash
pnpm install
```

#### 3. Copy environment variables

```bash
cp apps/web/.env.sample -> apps/web/.env
```

Then update the .env file with your configuration:

```
# Site Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SITE_TITLE="PDF Online Editor - Pdfily"
NEXT_PUBLIC_SITE_DESCRIPTION="PDF Online Editor - Pdfily offers a powerful, intuitive platform for editing, merging, splitting, and compressing PDF files, making document management simple and efficient."

# NEXT_PUBLIC_PDF_EDITOR: nutrient | pdftron
NEXT_PUBLIC_PDF_EDITOR=nutrient
NEXT_PUBLIC_PDF_VIEWER_LICENSE_KEY=demo:1744178434515:613a66bf0300000000bcd818ddea5f8c8b67eba6686d8c4376ab367e7b
NEXT_PUBLIC_PDF_NUTRIENT_LICENSED=false
NEXT_PUBLIC_PDF_NUTRIENT_LICENSE_KEY=

# Update these with your Supabase details from your project settings > API
# https://app.supabase.com/project/_/settings/api
NEXT_PUBLIC_SUPABASE_URL=https://plnzjnkqobruycuiykrk.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.-1YUjej0W_8_UbkxhUpSeq0dLVAwFOBvDp6uARnU5kM
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.P4TWbqrQxl8iZFtX0bsVhZpdyq16BTYRJJgDDI1i3WI

# Supabase Project Reference and Database Password
# These variables are used during Supabase project linking and database deployment.
# - SUPABASE_PROJECT_REF: The unique reference ID of your Supabase project.
# - SUPABASE_DB_PASSWORD: The password for the database, required for authentication during deployment.
SUPABASE_PROJECT_REF=plnzjnkqobruycuiykrk
SUPABASE_DB_PASSWORD=

# Captcha Configuration
NEXT_PUBLIC_CAPTCHA_SITE_KEY=0x4AAAAAABDgoJqKHoXCmgMT
NEXT_PUBLIC_CAPTCHA_ENABLED=false
CAPTCHA_SECRET_KEY=

# S3 Storage Configuration
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_HOST=https://plnzjnkqobruycuiykrk.supabase.co/storage/v1/s3
S3_REGION=us-east-1
S3_BUCKET=uploads

# SMTP Mail Configuration
SMTP_USER=apikey
SMTP_PASS=
SMTP_FROM_EMAIL=<EMAIL>
SMTP_SENDER_NAME=Admin

# Google Auth Configuration
NEXT_PUBLIC_GOOGLE_AUTH_ENABLED=false
GOOGLE_AUTH_CLIENT_ID=
GOOGLE_AUTH_SECRET_KEY=

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SIGNING_SECRET=
# Stripe Product and Price IDs
NEXT_PUBLIC_STRIPE_SUBSCRIPTION_PRICE_ID=
NEXT_PUBLIC_STRIPE_4_WEEKS_SUBSCRIPTOIN_PRICE_ID=

# Tracking
NEXT_PUBLIC_GOOGLE_TRACKING_ENABLED=
NEXT_PUBLIC_GTM_CONTAINER_ID=

NEXT_PUBLIC_POSTHOG_TRACKING_ENABLED=
NEXT_PUBLIC_POSTHOG_API_KEY=
NEXT_PUBLIC_POSTHOG_HOST=

# Auth
FILE_PREVIEW_JWT_SECRET=

# Locale Prefix Strategy
# 'never' = No language codes in URLs (e.g., /pricing)
# 'as-needed' = Language codes only for non-default locales (e.g., /pricing, /de/pricing)
# 'always' = Language codes for all locales (e.g., /en/pricing, /de/pricing)
NEXT_PUBLIC_LOCALE_PREFIX=never

# SendGrid
SENDGRID_SUPPORT_RECEIVER_EMAIL=<EMAIL>
SENDGRID_CONTACT_TEMPLATE_ID=d-915be223d69c4a83b9eda04822ed7cf3

```

#### 4. Start the development server

Then run the following command to start web:

```bash
pnpm run dev
# or
npm run dev
```

Then open [http://localhost:3000](http://localhost:3000) with your browser to see the result.
